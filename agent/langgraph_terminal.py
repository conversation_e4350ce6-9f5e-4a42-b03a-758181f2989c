"""
LangGraph Terminal Interface for Crawl4AI Agent

This module provides a natural language terminal interface that uses
the LangGraph agent to process user requests.
"""

import asyncio
import j<PERSON>
from typing import Dict, Any
from agent.langgraph_workflow import CrawlAgent
from utils.logger import logger


class LangGraphTerminal:
    """
    Terminal interface for the LangGraph-based Crawl4AI agent.
    """

    def __init__(self):
        """Initialize the terminal with the LangGraph agent."""
        self.agent = CrawlAgent()
        self.thread_id = "terminal_session"

    def display_welcome(self):
        """Display welcome message and usage instructions."""
        print("🚀 Crawl4AI LangGraph Agent - Natural Language Interface")
        print("=" * 60)
        print("You can now use natural language to interact with the crawler!")
        print()
        print("Examples:")
        print("  • crawl imprint from example.com")
        print("  • extract legal info from local example.com")
        print("  • summarize https://example.com --screenshot")
        print("  • find company data for example.com")
        print("  • smart crawl https://example.com for product data")
        print("  • extract sitemap from docs.python.org")
        print("  • get sitemap from https://example.com")
        print("  • search for python web scraping")
        print("  • search and crawl best web crawlers")
        print("  • process local file /path/to/file.html")
        print("  • what can you do?")
        print()
        print("Features:")
        print("  • Natural language understanding")
        print("  • Automatic intent parsing")
        print("  • AI-powered schema learning for structured data extraction")
        print("  • Sitemap extraction and URL discovery")
        print("  • Web search with DuckDuckGo")
        print("  • Search and crawl top results")
        print("  • Support for screenshots (--screenshot) and PDFs (--pdf)")
        print("  • Local file processing")
        print("  • Company data extraction")
        print("  • Content summarization")
        print()
        print("Type 'exit' to quit, 'help' for more info, or 'graph' to see workflow.")
        print("=" * 60)
        print()

    def display_help(self):
        """Display detailed help information."""
        print("\n📖 Crawl4AI LangGraph Agent - Help")
        print("=" * 50)
        print()
        print("NATURAL LANGUAGE COMMANDS:")
        print("  The agent understands natural language! Just describe what you want:")
        print()
        print("  Website Crawling:")
        print("    • 'crawl example.com'")
        print("    • 'get content from https://example.com'")
        print("    • 'fetch website example.com with screenshot'")
        print()
        print("  Legal/Impressum Extraction:")
        print("    • 'find impressum from example.com'")
        print("    • 'extract legal info from example.com'")
        print("    • 'get company data from example.com'")
        print()
        print("  Content Summarization:")
        print("    • 'summarize example.com'")
        print("    • 'create summary of https://example.com'")
        print()
        print("  Web Search:")
        print("    • 'search for python web scraping'")
        print("    • 'find information about AI agents'")
        print("    • 'search and crawl best web crawlers'")
        print()
        print("  Local File Processing:")
        print("    • 'process local file /path/to/file.html'")
        print("    • 'extract data from local example.com'")
        print("    • 'summarize local file /path/to/file.html'")
        print()
        print("  Media Options:")
        print("    • Add '--screenshot' to capture screenshots")
        print("    • Add '--pdf' to generate PDFs")
        print("    • Example: 'crawl example.com --screenshot --pdf'")
        print()
        print("  General Chat:")
        print("    • 'what can you do?'")
        print("    • 'how does web crawling work?'")
        print("    • 'explain impressum extraction'")
        print()
        print("SPECIAL COMMANDS:")
        print("  • 'exit' - Quit the application")
        print("  • 'help' - Show this help message")
        print("  • 'graph' - Display workflow visualization")
        print("  • 'clear' - Clear the screen")
        print()
        print("=" * 50)
        print()

    def display_result(self, result: Dict[str, Any]):
        """
        Display the result from the agent in a user-friendly format.

        Args:
            result: Result dictionary from the agent
        """
        if result and "error" in result:
            print(f"❌ Error: {result['error']}")
            return

        if not result:
            print("⚠️  No result data available")
            return

        print("✅ Success!")
        print("-" * 40)

        # Display summary if available
        if result and "summary" in result:
            print("📄 Summary:")
            print(result["summary"])
            print()

        # Display company data if available
        if result and "company_data" in result:
            print("🏢 Company Data:")
            company_data = result["company_data"]
            if isinstance(company_data, dict):
                for key, value in company_data.items():
                    if value:  # Only show non-empty values
                        print(f"  • {key.replace('_', ' ').title()}: {value}")
            else:
                print(f"  {company_data}")
            print()

        # Display content if available (and no summary)
        if result and "content" in result and "summary" not in result:
            content = result["content"]
            if len(content) > 500:
                print("📝 Content (first 500 characters):")
                print(content[:500] + "...")
            else:
                print("📝 Content:")
                print(content)
            print()

        # Display search results if available
        if result and "search_results" in result:
            print("🔍 Search Results:")
            search_results = result["search_results"]
            # Show top 5
            for i, search_result in enumerate(search_results[:5], 1):
                print(f"  {i}. {search_result.get('title', 'No title')}")
                print(f"     URL: {search_result.get('url', 'No URL')}")
                snippet = search_result.get('snippet', 'No description')
                if len(snippet) > 100:
                    snippet = snippet[:100] + "..."
                print(f"     {snippet}")
                print()

        # Display crawled search results if available
        if result and "crawled_search_results" in result:
            print("🕷️ Crawled Search Results:")
            crawled_results = result["crawled_search_results"]
            successful_crawls = [
                r for r in crawled_results if "error" not in r]
            failed_crawls = [r for r in crawled_results if "error" in r]

            print(
                f"  Successfully crawled: {len(successful_crawls)}/{len(crawled_results)} pages")

            # Show top 3 successful crawls
            for crawled_result in successful_crawls[:3]:
                print(f"  • {crawled_result.get('title', 'No title')}")
                print(f"    URL: {crawled_result.get('url', 'No URL')}")
                content = crawled_result.get('markdown_content', '')
                if len(content) > 200:
                    content = content[:200] + "..."
                print(f"    Content: {content}")
                if crawled_result.get('saved_html_path'):
                    print(f"    Saved: {crawled_result['saved_html_path']}")
                print()

            if failed_crawls:
                print(f"  ⚠️ Failed to crawl {len(failed_crawls)} pages")

        # Display multiple impressum results if available
        if result and "multiple_impressum_results" in result:
            print("🏢 Multiple Impressum Results:")
            impressum_results = result["multiple_impressum_results"]
            successful_results = [
                r for r in impressum_results if r.get("success")]
            failed_results = [
                r for r in impressum_results if not r.get("success")]

            print(
                f"  📊 Total: {len(impressum_results)} | ✅ Success: {len(successful_results)} | ❌ Failed: {len(failed_results)}")
            print()

            # Show successful results
            for i, impressum_result in enumerate(successful_results, 1):
                print(f"  {i}. ✅ {impressum_result.get('url', 'Unknown URL')}")
                content = impressum_result.get('markdown_content', '')
                if content and len(content) > 200:
                    content = content[:200] + "..."
                if content:
                    print(f"     📝 Content: {content}")
                if impressum_result.get('html_path'):
                    print(f"     💾 Saved: {impressum_result['html_path']}")
                print()

            # Show failed results
            if failed_results:
                print("  ❌ Failed URLs:")
                for impressum_result in failed_results:
                    print(
                        f"    • {impressum_result.get('url', 'Unknown URL')}: {impressum_result.get('error', 'Unknown error')}")
                print()

        # Display multiple crawl results if available
        if result and "multiple_crawl_results" in result:
            print("🌐 Multiple Crawl Results:")
            crawl_results = result["multiple_crawl_results"]
            successful_results = [r for r in crawl_results if r.get("success")]
            failed_results = [r for r in crawl_results if not r.get("success")]

            print(
                f"  📊 Total: {len(crawl_results)} | ✅ Success: {len(successful_results)} | ❌ Failed: {len(failed_results)}")
            print()

            # Show successful results
            for i, crawl_result in enumerate(successful_results, 1):
                print(f"  {i}. ✅ {crawl_result.get('url', 'Unknown URL')}")
                if crawl_result.get('page_title'):
                    print(f"     📄 Title: {crawl_result['page_title']}")
                content = crawl_result.get('markdown_content', '')
                if content and len(content) > 200:
                    content = content[:200] + "..."
                if content:
                    print(f"     📝 Content: {content}")
                if crawl_result.get('html_path'):
                    print(f"     💾 Saved: {crawl_result['html_path']}")
                print()

            # Show failed results
            if failed_results:
                print("  ❌ Failed URLs:")
                for crawl_result in failed_results:
                    print(
                        f"    • {crawl_result.get('url', 'Unknown URL')}: {crawl_result.get('error', 'Unknown error')}")
                print()

        # Display chat response if available
        if result and "response" in result:
            print("💬 Response:")
            print(result["response"])
            print()

        # Display file paths if available
        if result and "files" in result:
            print("📁 Saved Files:")
            files = result["files"]
            for file_type, path in files.items():
                if path:
                    print(f"  • {file_type.title()}: {path}")
            print()

    def display_workflow_graph(self):
        """Display detailed information about the workflow graph."""
        try:
            graph = self.agent.get_workflow_graph()

            print("\n🔄 Crawl4AI LangGraph Workflow - Detailed Structure")
            print("=" * 80)

            # Get nodes and edges from the graph
            nodes = graph.nodes if hasattr(graph, 'nodes') else {}
            edges = graph.edges if hasattr(graph, 'edges') else []

            # Node descriptions mapping
            node_descriptions = {
                "intent_parser": "🧠 Analyzes natural language input and determines workflow path",
                "url_crawler": "🌐 Crawls single websites and captures content with media options",
                "impressum_finder": "🏢 Finds and extracts legal notice/company information",
                "local_file_processor": "📁 Processes local HTML files for content extraction",
                "web_searcher": "🔍 Performs DuckDuckGo web searches with natural language queries",
                "search_and_crawler": "🔍🕷️ Searches web and automatically crawls top results",
                "link_extractor": "🔗 Extracts and categorizes internal/external links with metadata",
                "crawl_multiple_websites": "🌐📊 Batch processes multiple URLs for efficient crawling",
                "generate_multiple_summaries": "📄📊 Creates summaries for multiple crawled websites",
                "crawl_multiple_impressums": "🏢📊 Batch extracts legal data from multiple websites",
                "smart_crawler": "🤖 AI-powered schema learning for structured data extraction",
                "sitemap_extractor": "🗺️ Extracts and parses website sitemaps for URL discovery",
                "sitemap_multiple": "🗺️📊 Processes multiple sitemaps for comprehensive URL mapping",
                "download_html": "💾 Downloads and stores HTML content locally with media capture",
                "download_multiple_html": "💾📊 Batch downloads HTML content from multiple URLs",
                "data_extractor": "⚙️ Extracts structured company/legal data using LLM processing",
                "summarizer": "📝 Generates AI-powered content summaries and insights",
                "result_formatter": "📋 Formats final output for user-friendly presentation",
                "chat_responder": "💬 Handles general questions and conversational interactions",
                "statistics_summary": "📊 Provides crawl statistics and performance analytics",
                "domain_analysis": "🔍 Analyzes domain-specific crawling patterns and performance",
                "recent_activity": "⏰ Shows recent crawling activity and trends",
                "preview_historical_import": "👀 Previews historical data import operations",
                "import_historical_data": "📥 Imports historical crawl data into statistics database"
            }

            # Display all nodes with descriptions
            print("\n📋 WORKFLOW NODES:")
            print("-" * 50)

            node_count = 0
            for node_id in sorted(nodes.keys()) if nodes else sorted(node_descriptions.keys()):
                node_count += 1
                description = node_descriptions.get(
                    node_id, f"🔧 {node_id.replace('_', ' ').title()}")
                print(f"{node_count:2d}. {description}")
                print(f"    Node ID: {node_id}")
                print()

            # Display routing information
            print("\n🔀 WORKFLOW ROUTING:")
            print("-" * 50)
            print("📍 ENTRY POINT: intent_parser")
            print("   ↓")
            print(
                "🎯 MAIN ROUTING HUB: Routes to 21+ specialized nodes based on user intent")
            print()

            # Show key routing paths
            routing_paths = {
                "Website Crawling": ["intent_parser", "url_crawler", "data_extractor/summarizer", "result_formatter"],
                "Legal Data Extraction": ["intent_parser", "impressum_finder", "data_extractor", "result_formatter"],
                "Web Search & Crawl": ["intent_parser", "search_and_crawler", "result_formatter"],
                "Batch Operations": ["intent_parser", "crawl_multiple_websites", "generate_multiple_summaries", "result_formatter"],
                "Smart Crawling": ["intent_parser", "smart_crawler", "result_formatter"],
                "Statistics Analysis": ["intent_parser", "statistics_summary/domain_analysis", "result_formatter"]
            }

            for path_name, path_nodes in routing_paths.items():
                print(f"🛤️  {path_name}:")
                path_display = " → ".join(path_nodes)
                print(f"   {path_display}")
                print()

            # Display conditional routing logic
            print("\n⚡ CONDITIONAL ROUTING LOGIC:")
            print("-" * 50)
            print("🧠 Intent Parser routes based on:")
            print("   • Command keywords (crawl, impressum, search, smart, etc.)")
            print("   • URL patterns and validation")
            print("   • Media options (--screenshot, --pdf)")
            print("   • Batch indicators (multiple URLs, comma-separated)")
            print()
            print("🔄 Processing Nodes route based on:")
            print("   • Content type and structure")
            print("   • User intent (extract data vs. summarize)")
            print("   • Success/failure states")
            print("   • Available data in workflow state")
            print()

            # Display error handling
            print("\n🛡️ ERROR HANDLING:")
            print("-" * 50)
            print("• All nodes have error state routing to prevent workflow crashes")
            print("• Timeout protection (5-minute workflow timeout)")
            print("• Graceful degradation for failed operations")
            print("• Comprehensive logging for debugging")
            print()

            # Display state management
            print("\n💾 STATE MANAGEMENT:")
            print("-" * 50)
            print("• Persistent conversation memory across interactions")
            print("• Thread-based session management")
            print("• Rich state schema with 40+ fields")
            print("• Automatic state validation and error recovery")
            print()

            # Display supported operations
            print("\n🚀 SUPPORTED OPERATIONS:")
            print("-" * 50)
            operations = [
                "Single & batch website crawling",
                "Legal notice (impressum) extraction",
                "Web search with result crawling",
                "Smart crawling with schema learning",
                "Sitemap extraction and URL discovery",
                "Local HTML file processing",
                "Content summarization",
                "Link extraction and analysis",
                "HTML download with media capture",
                "Crawl statistics and analytics",
                "Historical data import/analysis",
                "General chat and help"
            ]

            for i, operation in enumerate(operations, 1):
                print(f"{i:2d}. {operation}")

            print()
            print("=" * 80)
            print("💡 TIP: Use natural language commands - the workflow automatically")
            print("    determines the best path based on your request!")
            print("=" * 80)
            print()

        except Exception as e:
            print(f"❌ Error displaying workflow: {e}")
            import traceback
            traceback.print_exc()

    async def run(self):
        """Run the interactive terminal loop."""
        self.display_welcome()

        while True:
            try:
                # Get user input
                user_input = input("🧠 Your request: ").strip()

                # Handle special commands
                if user_input.lower() == "exit":
                    print("👋 Goodbye!")
                    break
                elif user_input.lower() == "help":
                    self.display_help()
                    continue
                elif user_input.lower() == "graph":
                    self.display_workflow_graph()
                    continue
                elif user_input.lower() == "clear":
                    print("\033[2J\033[H")  # Clear screen
                    continue
                elif not user_input:
                    continue

                # Process the request through the LangGraph agent
                print("🔄 Processing your request...")
                result = await self.agent.process_request(user_input, self.thread_id)

                # Display the result
                try:
                    self.display_result(result)
                except Exception as display_error:
                    logger.error(f"Display error: {display_error}")
                    import traceback
                    traceback.print_exc()
                    print(f"❌ Error displaying result: {display_error}")
                    print(f"📋 Result type: {type(result)}")
                    if result:
                        print(
                            f"📋 Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
                    else:
                        print("📋 Result is None or empty")

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                logger.error(f"Terminal error: {e}")
                print(f"❌ An unexpected error occurred: {e}")
                print("Please try again or type 'help' for assistance.")


async def main():
    """Main entry point for the LangGraph terminal interface."""
    terminal = LangGraphTerminal()
    await terminal.run()


if __name__ == "__main__":
    asyncio.run(main())
